import { queryExpressTrack } from '@/api/order'
import { useRouter } from '@tarojs/taro'
import { useEffect } from 'react'
import { useAsyncFn } from 'react-use'

const OrderExpress = () => {
  const router = useRouter()

  const { expressId } = router.params

  // 获取地址详情
  const [expressState, expressFetch] = useAsyncFn(async () => {
    if (!expressId) return
    const res = await queryExpressTrack(expressId)
    console.log('res', res)
    return res.data
  }, [])

  useEffect(() => {
    expressFetch()
  }, [])

  return <></>
}

export default OrderExpress
