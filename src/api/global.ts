import { api1Request } from '@/utils/request'

// 响应接口
export interface GetStsTokenRes {
  data: {
    credentials: {
      securityToken: string
      accessKeySecret: string
      accessKeyId: string
      expiration: string
    }
    cdnUrl: string
    bucketName: string
    preUrl: string
    endpoint: string
  }
}

/**
 * 获取权鉴安全令牌
 * @returns
 */
export function getStsToken(): Promise<GetStsTokenRes> {
  return api1Request.get({
    url: `/api/anon/oss/sts`
  })
}

// 响应接口
export interface GetMiniStsTokenRes {
  data: {
    security_token: string
    signature: string
    x_oss_signature_version: string
    x_oss_credential: string
    x_oss_date: string
    policy: string
  }
}

/**
 * 获取小程序权鉴安全令牌
 * @returns
 */
export function getMiniStsToken(): Promise<GetMiniStsTokenRes> {
  return api1Request.get({
    url: `/api/v1/oss/anon/mini/sts`
  })
}
